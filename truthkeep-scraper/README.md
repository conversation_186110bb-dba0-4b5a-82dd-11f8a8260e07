# Truthkeep EEVblog Scraper

AWS Lambda-based scraper for EEVblog forums that extracts posts, metadata, and uploads to S3.

## Features

- Daily automated scraping of new posts
- Historical data scraping with pagination support
- Authentication support for private threads
- Rate limiting (one request at a time)
- S3 storage with date-based organization
- SQS-based workload distribution for large historical scrapes

## Architecture

- **Daily Scraper Lambda**: Runs on schedule to fetch new posts
- **Historical Scraper Lambda**: Processes batches of historical data via SQS
- **Historical Coordinator Lambda**: Splits large historical jobs into manageable batches

## Prerequisites

- Node.js 18.x
- AWS CLI configured
- Serverless Framework (`npm install -g serverless`)

## Setup

1. Clone the repository and install dependencies:
```bash
npm install
```

2. Copy `.env.example` to `.env` and configure:
```bash
cp .env.example .env
```

3. Install Puppeteer dependencies for local testing:
```bash
npm install puppeteer
```

## Local Development

Test the daily scraper locally:
```bash
npm run scrape:daily
```

## Deployment

1. Install serverless dependencies:
```bash
npm install -g serverless
serverless plugin install -n serverless-plugin-chrome
```

2. Deploy to AWS:
```bash
serverless deploy --stage prod
```

## Usage

### Daily Scraping
The daily scraper runs automatically at 2 AM UTC via EventBridge schedule.

### Historical Data Scraping
Invoke the historical coordinator to scrape past data:
```bash
serverless invoke -f historicalCoordinator --data '{"totalPages": 100, "pagesPerBatch": 5}'
```

## Data Format

Scraped data is stored in S3 with the following structure:
```json
{
  "site": {
    "name": "EEVblog",
    "url": "https://www.eevblog.com/forum/"
  },
  "scrapeDate": "2025-08-15T10:00:00Z",
  "posts": [
    {
      "id": "string",
      "title": "string",
      "text": "string",
      "url": "string",
      "viewCount": 0,
      "replyCount": 0,
      "forum": {
        "id": "string",
        "name": "string"
      },
      "replies": []
    }
  ]
}
```

## Monitoring

- CloudWatch Logs: Check `/aws/lambda/truthkeep-eevblog-scraper-*` log groups
- SQS Dead Letter Queue: Monitor failed historical scrape attempts
- S3 Bucket: Verify daily uploads in the configured bucket

## Environment Variables

- `EEVBLOG_USERNAME`: Forum login username
- `EEVBLOG_PASSWORD`: Forum login password
- `S3_BUCKET_NAME`: Target S3 bucket for data storage
- `AWS_REGION`: AWS region (default: us-east-1)