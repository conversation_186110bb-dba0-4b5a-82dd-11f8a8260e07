model SalesforceSite {
  id        Int               @id @default(autoincrement())
  name      String
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
  forums    SalesforceForum[]

  @@unique([name])
}

model SalesforceForum {
  id        Int      @id @default(autoincrement())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  salesforceSiteId Int
  site             SalesforceSite @relation(fields: [salesforceSiteId], references: [id], onDelete: Cascade)

  parentForumId Int?
  parentForum   SalesforceForum?  @relation("SubForum", fields: [parentForumId], references: [id])
  subForums     SalesforceForum[] @relation("SubForum")

  posts SalesforcePost[]

  @@unique([salesforceSiteId, name])
}

model SalesforcePost {
  id          String   @id
  title       String
  text        String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime
  viewCount   Int      @default(0)
  url         String

  memberLikeCount    Int @default(0)
  memberDislikeCount Int @default(0)
  guestLikeCount     Int @default(0)
  guestDislikeCount  Int @default(0)

  forumId Int
  forum   SalesforceForum       @relation(fields: [forumId], references: [id], onDelete: Cascade)
  replies SalesforcePostReply[]
}

model SalesforcePostReply {
  id             String   @id
  text           String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  publishedAt    DateTime
  name           String
  userScreenName String?

  memberLikeCount    Int @default(0)
  memberDislikeCount Int @default(0)
  guestLikeCount     Int @default(0)
  guestDislikeCount  Int @default(0)

  postId String
  post   SalesforcePost @relation(fields: [postId], references: [id], onDelete: Cascade)
}