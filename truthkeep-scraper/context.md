Hi <PERSON>,

Great meeting with you earlier this week.

Attached is the prompt for the project. The data source we'd like you to scrape is the EEVblog forums: https://www.eevblog.com/forum/

Please review the document and let either <PERSON> (CC'd here) or me know if you have any questions.

Looking forward to seeing your results and getting this project underway.

Best,

<PERSON> | CEO and Founder
<PERSON>@Truthkeep.ai | Truthkeep.ai
<PERSON>, Arizona
C: (602) 327-0904
 One attachment
  •  Scanned by Gmail

Al-Ekra<PERSON> <<EMAIL>>
Sun, Jul 13, 4:34 PM
to <PERSON>, <PERSON> and <PERSON>

It was also nice to talk with you guys last week, Thanks for sending the take home projects.Before starting the projects I have some queries. Would love to get feedbacks. 

Data Extraction Scope: What specific data points do you want from EEVblog threads (e.g., full post content, just titles/authors/timestamps, attachments/images, or metadata like views/replies)? Which forums/sections to target (e.g., all, or focus on /repairs/ and /projects/)?
Normalization Format: Can you provide a sample JSON schema or example output from existing sources (Reddit/YouTube/Salesforce) so I can match it exactly? Any required fields like sentiment flags or risk indicators?
Scraping Schedule & Volume: How often should it run (e.g., daily cron, real-time on new posts via RSS, or event-triggered)? What's the expected volume (e.g., crawl last 30 days initially, or full historical)?
Integration Details: What's the endpoint/signature for the analysis microservice (e.g., POST /analyze with JSON body)? Any auth (e.g., API keys, headers)? For AWS, which region/account to use, and do you have existing IAM roles/policies I should reference?
Auth & Access: Does the scraper need to handle logins for private threads? Any API access to EEVblog (if available) or proxies to avoid blocks?
Edge Cases & Constraints: What about pagination, infinite scrolling, or anti-scraping measures? Any legal/compliance rules (e.g., data retention limits, avoid certain user data)? Budget caps for AWS costs?

Best
Al

Anthony Fierro <<EMAIL>>
Mon, Jul 14, 4:30 PM
to me, William

Answers to your questions below. We will kick this off next week as this week is very busy for us.

Thanks,
Anthony

On Sun, Jul 13, 2025 at 6:34 PM Al-Ekram Elahee Hridoy <<EMAIL>> wrote:
Hi Will and Anthony

It was also nice to talk with you guys last week, Thanks for sending the take home projects.Before starting the projects I have some queries. Would love to get feedbacks. 

Data Extraction Scope: What specific data points do you want from EEVblog threads (e.g., full post content, just titles/authors/timestamps, attachments/images, or metadata like views/replies)?

Full post content, no images or attachments. We also need views, replies, number of shares or likes, whatever we have access to

Which forums/sections to target (e.g., all, or focus on /repairs/ and /projects/)?

All, the AI will determine if it thinks something is relevant to the client

Normalization Format: Can you provide a sample JSON schema or example output from existing sources (Reddit/YouTube/Salesforce) so I can match it exactly?

We will get you this next week, we are going to kick this off Monday

Any required fields like sentiment flags or risk indicators?

No the AI will handle this


Scraping Schedule & Volume: How often should it run (e.g., daily cron, real-time on new posts via RSS, or event-triggered)?

Daily

What's the expected volume (e.g., crawl last 30 days initially, or full historical)?

Ability to crawl last X weeks or months, what do you think is possible here?


Integration Details: What's the endpoint/signature for the analysis microservice (e.g., POST /analyze with JSON body)?

Will give you this next week

Any auth (e.g., API keys, headers)? For AWS, which region/account to use, and do you have existing IAM roles/policies I should reference?

We will give you AWS credentials for the scraping service to hit our API


Auth & Access: Does the scraper need to handle logins for private threads? Any API access to EEVblog (if available) or proxies to avoid blocks?


Private threads would be good to use, proxies we would like to see added effort to implement


Edge Cases & Constraints: What about pagination, infinite scrolling, or anti-scraping measures?

Please give us your analysis on if you think the source is using anti-scraping measures

Any legal/compliance rules (e.g., data retention limits, avoid certain user data)? Budget caps for AWS costs?

No but lets keep it reasonable

Al-Ekram Elahee Hridoy <<EMAIL>>
Tue, Jul 15, 7:57 PM
to Anthony, William

Thanks Anthony for the clarification , eagerly looking forward to it.

Best
Al

---Project
Truthkeep Project Brief: Adding a New Data Source to AI
Insights Platform
Project Overview:
Our platform scrapes data from Reddit, YouTube, and Salesforce and analyzes it using AI to
extract trends, insights, and risks. We're expanding the platform by integrating a new data
source.
Your Role:
You will be responsible for building the scraper for this new data source, integrating it into our
existing AWS infrastructure and codebase (NestJS backend, React frontend).

✅ Objectives
● Design and implement a scalable scraper for the new data source.
● Implement it on AWS-based architecture.
● Normalize data to a standard format and pass it through to our analysis microservice.

Next Steps

1. Proposed Approach
○ Brief technical overview of how you plan to implement the scraper.
○ Tools/libraries you plan to use.
○ Thoughts on error handling, retries, rate-limiting, risks, etc.
○ Estimated completion time + Cost to Complete
○ Any questions you have for us

----
Anthony Fierro
Thu, Jul 31, 11:49 AM
to me

Hey Al, I wanted to reach out and see if you were still interested in taking this on. Did you have a chance to scope a price for the project? We're ready to start if you are!

Thanks,
Anthony

Al-Ekram Elahee Hridoy
Tue, Aug 12, 3:54 PM (9 days ago)
Hi Anthony, Hope you're doing well. I'm so sorry for the delayed responses; I was busy for the last two weeks due to some unavoidable circumstances. I'm startin

Anthony Fierro
Wed, Aug 13, 5:48 PM (8 days ago)
Hey Al, its great to hear back from you. 1. Login credentials username: 0813 password: Qwalop125! Will Ginsberg is our dev lead and can answer the other questio

William Ginsberg <<EMAIL>>
Attachments
Thu, Aug 14, 9:20 AM (7 days ago)
to will.j.ginsberg, Anthony, me

Hi Al

Looks to me like thankfully everything we need on EEVblog can be accessed without executing any javascript.

In terms of the schema/API  - your scraper can upload the results as JSON to an S3 bucket and the exact format is not super important. As long as the objects in S3 have the current date as the key prefix so we can look up the most recent data each day. I attached an example of our data model for a rough idea of what it'll end up looking like.

2 - I would prefer Lambda.

3 - At the moment we wouldn't have any use for those fields, but if it's really low effort I think it would be better to save everything we can.

4 - I'm tempted to say just limit it to one request at a time and see if we can get away with that. One request every 10 seconds would make the historical data job kind of brutal. It looks like the daily job could run as one < 15 minute lambda execution but either way we should think about a way to split up the workload across multiple lambda executions.

Best,

Will Ginsberg

 One attachment
  •  Scanned by Gmail

Al-Ekram Elahee Hridoy <<EMAIL>>
Mon, Aug 18, 9:17 AM (3 days ago)
to William, Anthony, will.j.ginsberg


Hi William,
Awesome, thanks for the info! I’m sending my technical implementation scope soon and ready to start the project. Thanks 
Best
Al-Ekram


Best

---
model SalesforceSite {
  id        Int               @id @default(autoincrement())
  name      String
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
  forums    SalesforceForum[]

  @@unique([name])
}

model SalesforceForum {
  id        Int      @id @default(autoincrement())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  salesforceSiteId Int
  site             SalesforceSite @relation(fields: [salesforceSiteId], references: [id], onDelete: Cascade)

  parentForumId Int?
  parentForum   SalesforceForum?  @relation("SubForum", fields: [parentForumId], references: [id])
  subForums     SalesforceForum[] @relation("SubForum")

  posts SalesforcePost[]

  @@unique([salesforceSiteId, name])
}

model SalesforcePost {
  id          String   @id
  title       String
  text        String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime
  viewCount   Int      @default(0)
  url         String

  memberLikeCount    Int @default(0)
  memberDislikeCount Int @default(0)
  guestLikeCount     Int @default(0)
  guestDislikeCount  Int @default(0)

  forumId Int
  forum   SalesforceForum       @relation(fields: [forumId], references: [id], onDelete: Cascade)
  replies SalesforcePostReply[]
}

model SalesforcePostReply {
  id             String   @id
  text           String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  publishedAt    DateTime
  name           String
  userScreenName String?

  memberLikeCount    Int @default(0)
  memberDislikeCount Int @default(0)
  guestLikeCount     Int @default(0)
  guestDislikeCount  Int @default(0)

  postId String
  post   SalesforcePost @relation(fields: [postId], references: [id], onDelete: Cascade)
}