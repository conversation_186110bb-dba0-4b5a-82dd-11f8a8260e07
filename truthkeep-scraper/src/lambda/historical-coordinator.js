const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');

const sqsClient = new SQSClient({ region: process.env.AWS_REGION || 'us-east-1' });

exports.handler = async (event) => {
    console.log('Historical data coordinator invoked:', JSON.stringify(event));
    
    const {
        totalPages = 100,
        pagesPerBatch = 5,
        queueUrl = process.env.HISTORICAL_QUEUE_URL
    } = event;

    try {
        const batches = [];
        
        for (let i = 0; i < totalPages; i += pagesPerBatch) {
            const batch = {
                scrapeType: 'historical',
                startPage: i + 1,
                endPage: Math.min(i + pagesPerBatch, totalPages)
            };
            
            const command = new SendMessageCommand({
                QueueUrl: queueUrl,
                MessageBody: JSON.stringify(batch),
                MessageAttributes: {
                    batchId: {
                        DataType: 'String',
                        StringValue: `batch-${i / pagesPerBatch + 1}`
                    }
                }
            });
            
            await sqsClient.send(command);
            batches.push(batch);
        }

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Historical scraping jobs queued successfully',
                totalBatches: batches.length,
                batches
            })
        };
    } catch (error) {
        console.error('Error queuing historical jobs:', error);
        throw error;
    }
};