const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const EEVBlogScraper = require('../scrapers/eevblog-scraper');

const s3Client = new S3Client({ region: process.env.AWS_REGION || 'us-east-1' });

exports.handler = async (event) => {
    console.log('Lambda invocation with event:', JSON.stringify(event));
    
    try {
        const scraper = new EEVBlogScraper({
            username: process.env.EEVBLOG_USERNAME,
            password: process.env.EEVBLOG_PASSWORD,
            headless: true
        });

        let results;
        
        if (event.scrapeType === 'daily') {
            results = await scraper.scrapeDailyPosts();
        } else if (event.scrapeType === 'historical') {
            const { startPage, endPage } = event;
            results = await scraper.scrapeHistoricalPosts(startPage, endPage);
        } else {
            throw new Error('Invalid scrapeType. Must be "daily" or "historical"');
        }

        const uploadKey = generateS3Key(event.scrapeType);
        await uploadToS3(results, uploadKey);

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Scraping completed successfully',
                postsScraped: results.posts.length,
                s3Key: uploadKey
            })
        };
    } catch (error) {
        console.error('Scraping error:', error);
        throw error;
    }
};

function generateS3Key(scrapeType) {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = now.getTime();
    
    return `${year}/${month}/${day}/${scrapeType}-${timestamp}.json`;
}

async function uploadToS3(data, key) {
    const command = new PutObjectCommand({
        Bucket: process.env.S3_BUCKET_NAME,
        Key: key,
        Body: JSON.stringify(data, null, 2),
        ContentType: 'application/json'
    });
    
    await s3Client.send(command);
    console.log(`Data uploaded to S3: ${key}`);
}