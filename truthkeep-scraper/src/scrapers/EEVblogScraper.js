const axios = require('axios');
const cheerio = require('cheerio');

class EEVblogScraper {
  constructor(credentials) {
    this.baseUrl = 'https://www.eevblog.com/forum';
    this.credentials = credentials;
    this.session = null;
    this.rateLimitDelay = 10000; // 10 seconds as per robots.txt
    
    this.axiosInstance = axios.create({
      timeout: 30000,
      headers: {
        'User-Agent': 'TruthkeepBot/1.0 (Research purposes; contact: <EMAIL>)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      }
    });
  }

  async delay() {
    return new Promise(resolve => setTimeout(resolve, this.rateLimitDelay));
  }

  async authenticate() {
    try {
      // Get login page first
      const loginPageResponse = await this.axiosInstance.get(`${this.baseUrl}/index.php?action=login`);
      const $ = cheerio.load(loginPageResponse.data);
      
      // Extract the actual hidden field name (not 'sc')
      const hiddenInputs = {};
      $('input[type="hidden"]').each((i, el) => {
        const name = $(el).attr('name');
        const value = $(el).attr('value');
        if (name && value) {
          hiddenInputs[name] = value;
        }
      });

      console.log('Hidden inputs found:', hiddenInputs);

      const formData = new URLSearchParams({
        'user': this.credentials.username,
        'passwrd': this.credentials.password,
        'cookielength': '-1',
        ...hiddenInputs
      });

      await this.delay();

      // Submit login using the form action from the page
      const loginResponse = await this.axiosInstance.post(
        `${this.baseUrl}/login2/`,
        formData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Referer': `${this.baseUrl}/index.php?action=login`
          },
          maxRedirects: 5
        }
      );

      // Store cookies for session
      if (loginResponse.headers['set-cookie']) {
        this.session = loginResponse.headers['set-cookie'];
        console.log('Authentication successful - cookies received');
        return true;
      } else {
        console.log('Authentication may have failed - no cookies received');
        return false;
      }
    } catch (error) {
      console.error('Authentication failed:', error.message);
      return false;
    }
  }

  async getForumStructure() {
    try {
      const response = await this.axiosInstance.get(`${this.baseUrl}/index.php`, {
        headers: this.session ? { Cookie: this.session.join('; ') } : {}
      });
      
      const $ = cheerio.load(response.data);
      const forums = [];

      // Parse forum structure - each forum is in a tr with id="board_X"
      $('tr[id^="board_"]').each((index, element) => {
        const boardId = $(element).attr('id').replace('board_', '');
        const linkElement = $(element).find('.info .subject');
        const descElement = $(element).find('.info p');
        const statsElement = $(element).find('.stats p');
        
        if (linkElement.length > 0) {
          const href = linkElement.attr('href');
          const statsText = statsElement.text();
          const posts = statsText.match(/(\d+)\s*Posts/)?.[1] || '0';
          const topics = statsText.match(/(\d+)\s*Topics/)?.[1] || '0';
          
          forums.push({
            id: boardId,
            name: linkElement.text().trim(),
            description: descElement.text().trim(),
            url: href,
            posts: parseInt(posts),
            topics: parseInt(topics)
          });
        }
      });

      await this.delay();
      return forums;
    } catch (error) {
      console.error('Failed to get forum structure:', error.message);
      throw error;
    }
  }

  async scrapeBoard(boardUrl, pages = 1) {
    const posts = [];
    
    for (let page = 0; page < pages; page++) {
      try {
        // Handle pagination - EEVblog uses format like /forum/beginners/5/
        let url = boardUrl;
        if (page > 0) {
          // Add page number to URL
          url = boardUrl.endsWith('/') ? 
            `${boardUrl}${page * 20}/` : 
            `${boardUrl}/${page * 20}/`;
        }
        
        const response = await this.axiosInstance.get(url, {
          headers: this.session ? { Cookie: this.session.join('; ') } : {}
        });
        
        const $ = cheerio.load(response.data);
        
        // Debug: save first page HTML
        if (page === 0) {
          require('fs').writeFileSync(`/tmp/board-${boardUrl.split('/').pop()}.html`, response.data);
          console.log(`Board HTML saved for debugging`);
        }
        
        // Look for topic rows - each topic is in a tr with subject cell containing the title link
        $('tbody tr').each((index, element) => {
          const subjectCell = $(element).find('.subject');
          const titleElement = subjectCell.find('a').first();
          
          if (titleElement.length > 0 && titleElement.attr('href')?.includes('/forum/')) {
            const authorCell = $(element).find('.stats').first();
            const statsCell = $(element).find('.stats').last();
            const lastPostCell = $(element).find('.lastpost');
            
            const statsText = statsCell.text();
            const replies = statsText.match(/(\d+)\s*Replies/i)?.[1] || '0';
            const views = statsText.match(/(\d+)\s*Views/i)?.[1] || '0';
            
            posts.push({
              id: titleElement.attr('href')?.split('/').filter(p => p).pop(),
              title: titleElement.text().trim(),
              url: titleElement.attr('href'),
              author: authorCell.find('a[href*="/profile/"]').text().trim(),
              replies: replies,
              views: views,
              lastPost: {
                date: lastPostCell.text().match(/(?:Today|Yesterday|\w+ \d+, \d+).*?(?:am|pm)/)?.[0] || '',
                author: lastPostCell.find('a[href*="/profile/"]').text().trim()
              },
              boardUrl: boardUrl
            });
          }
        });

        await this.delay();
      } catch (error) {
        console.error(`Failed to scrape board ${boardUrl}, page ${page}:`, error.message);
      }
    }

    return posts;
  }

  async scrapePost(postUrl) {
    try {
      const response = await this.axiosInstance.get(postUrl, {
        headers: this.session ? { Cookie: this.session.join('; ') } : {}
      });
      
      const $ = cheerio.load(response.data);
      const posts = [];

      // Look for individual posts in the thread - each post is in a div with class 'post'
      $('.post, .post_wrapper, div[id^="msg_"]').each((index, element) => {
        const authorElement = $(element).find('.poster h4 a, .poster a');
        const contentElement = $(element).find('.post, .post_body, .inner').first();
        const timestampElement = $(element).find('.keyinfo, .smalltext');
        
        if (authorElement.length > 0 || contentElement.text().trim()) {
          posts.push({
            id: $(element).attr('id') || `post_${index}`,
            author: authorElement.text().trim() || 'Unknown',
            authorUrl: authorElement.attr('href'),
            content: contentElement.text().trim(),
            timestamp: timestampElement.text().trim(),
            likes: '0', // EEVblog doesn't seem to have likes
            postUrl: postUrl
          });
        }
      });

      await this.delay();
      return {
        topicId: postUrl.split('/').filter(p => p).pop(),
        title: $('h1, .catbg, .titlebg').first().text().trim(),
        posts: posts,
        views: $('td:contains("Views")').text().match(/(\d+)/)?.[1] || '0',
        replies: $('td:contains("Replies")').text().match(/(\d+)/)?.[1] || '0'
      };
    } catch (error) {
      console.error(`Failed to scrape post ${postUrl}:`, error.message);
      throw error;
    }
  }

  async scrapeDailyPosts(hoursBack = 24) {
    const forums = await this.getForumStructure();
    const allPosts = [];
    const cutoffTime = new Date(Date.now() - (hoursBack * 60 * 60 * 1000));

    for (const forum of forums) {
      if (forum.id) {
        const posts = await this.scrapeBoard(forum.id, 2); // Check first 2 pages
        
        // Filter posts from last 24 hours (basic filtering)
        const recentPosts = posts.filter(post => {
          // This is a simplified check - in reality you'd parse the date properly
          return true; // For now, return all from first 2 pages
        });
        
        allPosts.push(...recentPosts);
      }
    }

    return allPosts;
  }
}

module.exports = EEVblogScraper;
