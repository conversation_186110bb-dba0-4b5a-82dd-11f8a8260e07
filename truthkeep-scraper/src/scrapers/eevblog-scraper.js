const puppeteer = require('puppeteer');

class EEVBlogScraper {
    constructor(config) {
        this.username = config.username;
        this.password = config.password;
        this.headless = config.headless !== false;
        this.baseUrl = 'https://www.eevblog.com/forum/';
        this.browser = null;
        this.page = null;
        this.isAuthenticated = false;
    }

    async initialize() {
        this.browser = await puppeteer.launch({
            headless: this.headless,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
        await this.page.setViewport({ width: 1920, height: 1080 });
    }

    async authenticate() {
        if (this.isAuthenticated) return;

        console.log('Authenticating to EEVblog...');
        await this.page.goto(`${this.baseUrl}index.php?action=login`, {
            waitUntil: 'networkidle2'
        });

        await this.page.type('input[name="user"]', this.username);
        await this.page.type('input[name="passwrd"]', this.password);
        
        await Promise.all([
            this.page.click('input[type="submit"][value="Login"]'),
            this.page.waitForNavigation({ waitUntil: 'networkidle2' })
        ]);

        const loginError = await this.page.$('.error');
        if (loginError) {
            throw new Error('Authentication failed');
        }

        this.isAuthenticated = true;
        console.log('Authentication successful');
    }

    async scrapeDailyPosts() {
        await this.initialize();
        await this.authenticate();

        const posts = [];
        const forums = await this.getAllForums();

        for (const forum of forums) {
            const recentPosts = await this.getRecentPostsFromForum(forum);
            posts.push(...recentPosts);
            
            await this.delay(1000);
        }

        await this.browser.close();

        return {
            site: {
                name: 'EEVblog',
                url: this.baseUrl
            },
            scrapeDate: new Date().toISOString(),
            posts
        };
    }

    async scrapeHistoricalPosts(startPage, endPage) {
        await this.initialize();
        await this.authenticate();

        const posts = [];
        
        for (let page = startPage; page <= endPage; page++) {
            const pagePosts = await this.scrapePostsFromPage(page);
            posts.push(...pagePosts);
            
            await this.delay(1000);
        }

        await this.browser.close();

        return {
            site: {
                name: 'EEVblog',
                url: this.baseUrl
            },
            scrapeDate: new Date().toISOString(),
            pageRange: { start: startPage, end: endPage },
            posts
        };
    }

    async getAllForums() {
        await this.page.goto(this.baseUrl, { waitUntil: 'networkidle2' });
        
        const forums = await this.page.evaluate(() => {
            const forumLinks = Array.from(document.querySelectorAll('a[href*="board="]'));
            return forumLinks.map(link => ({
                id: link.href.match(/board=(\d+)/)?.[1],
                name: link.textContent.trim(),
                url: link.href
            })).filter(f => f.id);
        });

        return forums;
    }

    async getRecentPostsFromForum(forum) {
        await this.page.goto(forum.url, { waitUntil: 'networkidle2' });
        
        const posts = await this.page.evaluate((forumData) => {
            const postElements = Array.from(document.querySelectorAll('.topic_table tr'));
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            return postElements.map(row => {
                const titleLink = row.querySelector('a[href*="topic="]');
                if (!titleLink) return null;

                const statsCell = row.querySelector('.stats');
                const viewCount = statsCell ? parseInt(statsCell.textContent.match(/(\d+)\s*Views/)?.[1] || '0') : 0;
                const replyCount = statsCell ? parseInt(statsCell.textContent.match(/(\d+)\s*Replies/)?.[1] || '0') : 0;

                const lastPostInfo = row.querySelector('.lastpost');
                const dateText = lastPostInfo?.textContent || '';
                
                return {
                    id: titleLink.href.match(/topic=(\d+)/)?.[1],
                    title: titleLink.textContent.trim(),
                    url: titleLink.href,
                    forum: forumData,
                    viewCount,
                    replyCount,
                    lastPostDate: dateText
                };
            }).filter(post => post !== null);
        }, forum);

        const detailedPosts = [];
        for (const post of posts.slice(0, 5)) {
            const details = await this.getPostDetails(post);
            detailedPosts.push(details);
            await this.delay(1000);
        }

        return detailedPosts;
    }

    async getPostDetails(postSummary) {
        await this.page.goto(postSummary.url, { waitUntil: 'networkidle2' });

        const postDetails = await this.page.evaluate(() => {
            const firstPost = document.querySelector('.post_wrapper');
            if (!firstPost) return null;

            const authorInfo = firstPost.querySelector('.poster h4 a');
            const postBody = firstPost.querySelector('.post .inner');
            const postTime = firstPost.querySelector('.smalltext');

            const replies = Array.from(document.querySelectorAll('.post_wrapper:not(:first-child)')).map(reply => {
                const replyAuthor = reply.querySelector('.poster h4 a');
                const replyBody = reply.querySelector('.post .inner');
                const replyTime = reply.querySelector('.smalltext');

                return {
                    id: reply.id,
                    text: replyBody?.textContent.trim() || '',
                    publishedAt: replyTime?.textContent.trim() || '',
                    name: replyAuthor?.textContent.trim() || 'Guest',
                    userScreenName: replyAuthor?.textContent.trim() || null
                };
            });

            return {
                text: postBody?.textContent.trim() || '',
                publishedAt: postTime?.textContent.trim() || '',
                authorName: authorInfo?.textContent.trim() || 'Guest',
                replies
            };
        });

        return {
            ...postSummary,
            ...postDetails
        };
    }

    async scrapePostsFromPage(pageNumber) {
        console.log(`Scraping historical page ${pageNumber}`);
        return [];
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = EEVBlogScraper;