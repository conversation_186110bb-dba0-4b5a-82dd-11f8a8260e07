const EEVblogScraper = require('./src/scrapers/EEVblogScraper');
require('dotenv').config();

async function testScraper() {
  const scraper = new EEVblogScraper({
    username: '0813',
    password: 'Qwalop125!'
  });

  try {
    console.log('Testing authentication...');
    const authSuccess = await scraper.authenticate();
    
    if (!authSuccess) {
      console.log('Authentication failed');
      return;
    }

    console.log('Getting forum structure...');
    const forums = await scraper.getForumStructure();
    console.log(`Found ${forums.length} forums`);
    console.log('First 3 forums:', forums.slice(0, 3));

    if (forums.length > 0) {
      console.log('\nTesting board scraping...');
      const posts = await scraper.scrapeBoard(forums[0].url, 1);
      console.log(`Found ${posts.length} posts in first forum`);
      
      if (posts.length > 0) {
        console.log('First post:', posts[0]);
        
        console.log('\nTesting individual post scraping...');
        const fullPost = await scraper.scrapePost(posts[0].url);
        console.log('Full post data:', JSON.stringify(fullPost, null, 2));
      }
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testScraper();
