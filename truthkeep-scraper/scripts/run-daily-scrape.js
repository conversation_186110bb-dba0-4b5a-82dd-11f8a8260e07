require('dotenv').config();

const { handler } = require('../src/lambda/scraper-handler');

async function runDailyScrape() {
    console.log('Running daily scrape locally...');
    
    const event = {
        scrapeType: 'daily'
    };

    process.env.EEVBLOG_USERNAME = process.env.EEVBLOG_USERNAME || '0813';
    process.env.EEVBLOG_PASSWORD = process.env.EEVBLOG_PASSWORD || 'Qwalop125!';
    process.env.S3_BUCKET_NAME = process.env.S3_BUCKET_NAME || 'truthkeep-eevblog-data';

    try {
        const result = await handler(event);
        console.log('Scrape completed:', result);
    } catch (error) {
        console.error('<PERSON>rape failed:', error);
        process.exit(1);
    }
}

runDailyScrape();