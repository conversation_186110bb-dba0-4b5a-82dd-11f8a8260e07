# EEVblog Scraper Lambda Architecture Design

## Overview
Lambda-based scraper for EEVblog forums that extracts posts, metadata, and uploads to S3.

## Architecture Components

### 1. Main Lambda Functions

#### A. Daily Scraper Lambda
- **Purpose**: Scrape new posts from the last 24 hours
- **Trigger**: EventBridge scheduled rule (daily)
- **Timeout**: 15 minutes
- **Memory**: 1024 MB (for Puppeteer)

#### B. Historical Data Lambda
- **Purpose**: Scrape historical data (configurable time range)
- **Trigger**: Manual invocation or SQS queue
- **Timeout**: 15 minutes
- **Memory**: 1024 MB
- **Strategy**: Split work across multiple invocations using pagination

### 2. Supporting Infrastructure

#### S3 Bucket Structure
```
truthkeep-eevblog-data/
├── 2025/
│   ├── 08/
│   │   ├── 15/
│   │   │   ├── daily-scrape-12345.json
│   │   │   └── historical-page-1.json
```

#### SQS Queue (for historical data)
- **Purpose**: Coordinate multiple Lambda invocations
- **Messages**: Contain page ranges to scrape
- **Dead Letter Queue**: For failed scrapes

### 3. Data Model (based on Salesforce schema)

```json
{
  "site": {
    "name": "EEVblog",
    "url": "https://www.eevblog.com/forum/"
  },
  "forum": {
    "id": "string",
    "name": "string",
    "parentForumId": "string|null"
  },
  "post": {
    "id": "string",
    "title": "string",
    "text": "string",
    "publishedAt": "datetime",
    "viewCount": "number",
    "url": "string",
    "memberLikeCount": "number",
    "replies": [
      {
        "id": "string",
        "text": "string",
        "publishedAt": "datetime",
        "name": "string",
        "userScreenName": "string|null",
        "memberLikeCount": "number"
      }
    ]
  }
}
```

### 4. Scraping Strategy

1. **Authentication**: Login using provided credentials before scraping
2. **Rate Limiting**: One request at a time (no concurrent requests)
3. **Error Handling**: Exponential backoff with jitter
4. **Pagination**: Track last scraped position for resume capability

### 5. Implementation Phases

1. **Phase 1**: Basic scraper with authentication
2. **Phase 2**: S3 upload integration
3. **Phase 3**: Historical data splitting logic
4. **Phase 4**: Monitoring and error handling