service: truthkeep-eevblog-scraper

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region, 'us-east-1'}
  timeout: 900
  memorySize: 1024
  environment:
    EEVBLOG_USERNAME: ${env:EEVBLOG_USERNAME}
    EEVBLOG_PASSWORD: ${env:EEVBLOG_PASSWORD}
    S3_BUCKET_NAME: ${self:custom.s3BucketName}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - s3:PutObject
            - s3:PutObjectAcl
          Resource: arn:aws:s3:::${self:custom.s3BucketName}/*
        - Effect: Allow
          Action:
            - sqs:SendMessage
            - sqs:ReceiveMessage
            - sqs:DeleteMessage
          Resource: 
            - !GetAtt HistoricalDataQueue.Arn

custom:
  s3BucketName: truthkeep-eevblog-data-${self:provider.stage}

functions:
  dailyScraper:
    handler: src/lambda/scraper-handler.handler
    events:
      - schedule:
          rate: cron(0 2 * * ? *)
          input:
            scrapeType: daily
    layers:
      - arn:aws:lambda:${self:provider.region}:764866452798:layer:chrome-aws-lambda:25

  historicalScraper:
    handler: src/lambda/scraper-handler.handler
    events:
      - sqs:
          arn: !GetAtt HistoricalDataQueue.Arn
          batchSize: 1
    layers:
      - arn:aws:lambda:${self:provider.region}:764866452798:layer:chrome-aws-lambda:25

resources:
  Resources:
    S3Bucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.s3BucketName}
        LifecycleConfiguration:
          Rules:
            - Id: DeleteOldData
              Status: Enabled
              ExpirationInDays: 90

    HistoricalDataQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: truthkeep-historical-scrape-queue
        VisibilityTimeout: 960
        MessageRetentionPeriod: 1209600
        RedrivePolicy:
          deadLetterTargetArn: !GetAtt HistoricalDataDLQ.Arn
          maxReceiveCount: 3

    HistoricalDataDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: truthkeep-historical-scrape-dlq
        MessageRetentionPeriod: 1209600