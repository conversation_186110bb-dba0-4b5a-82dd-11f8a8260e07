service: eevblog-scraper

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  stage: ${opt:stage, 'dev'}
  timeout: 900 # 15 minutes max for Lambda
  memorySize: 3008 # Max memory for Puppeteer
  
  environment:
    FORUM_USERNAME: ${env:FORUM_USERNAME, '0813'}
    FORUM_PASSWORD: ${env:FORUM_PASSWORD, 'Qwalop125!'}
    S3_BUCKET: ${env:S3_BUCKET, 'eevblog-scraper-${self:provider.stage}'}
    SQS_QUEUE_URL: 
      Ref: HistoricalDataQueue
  
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - s3:PutObject
            - s3:GetObject
          Resource: arn:aws:s3:::${self:provider.environment.S3_BUCKET}/*
        - Effect: Allow
          Action:
            - sqs:SendMessage
            - sqs:ReceiveMessage
            - sqs:DeleteMessage
            - sqs:GetQueueAttributes
          Resource: 
            - Fn::GetAtt: [HistoricalDataQueue, Arn]
            - Fn::GetAtt: [HistoricalDataDLQ, Arn]

functions:
  dailyScraper:
    handler: src/handlers/dailyScraper.handler
    events:
      - schedule: cron(0 2 * * ? *) # 2 AM UTC daily
    layers:
      - arn:aws:lambda:${self:provider.region}:764866452798:layer:chrome-aws-lambda:39

  historicalScraper:
    handler: src/handlers/historicalScraper.handler
    events:
      - sqs:
          arn:
            Fn::GetAtt: [HistoricalDataQueue, Arn]
          batchSize: 1
    layers:
      - arn:aws:lambda:${self:provider.region}:764866452798:layer:chrome-aws-lambda:39

  coordinator:
    handler: src/handlers/coordinator.handler
    environment:
      QUEUE_URL:
        Ref: HistoricalDataQueue

resources:
  Resources:
    HistoricalDataQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: eevblog-historical-${self:provider.stage}
        VisibilityTimeout: 960 # 16 minutes (Lambda timeout + buffer)
        MessageRetentionPeriod: 1209600 # 14 days
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt: [HistoricalDataDLQ, Arn]
          maxReceiveCount: 3

    HistoricalDataDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: eevblog-historical-dlq-${self:provider.stage}
        MessageRetentionPeriod: 1209600 # 14 days

    S3Bucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:provider.environment.S3_BUCKET}
        LifecycleConfiguration:
          Rules:
            - Id: DeleteOldData
              Status: Enabled
              ExpirationInDays: 90

plugins:
  - serverless-webpack

custom:
  webpack:
    webpackConfig: ./webpack.config.js
    includeModules:
      forceExclude:
        - aws-sdk