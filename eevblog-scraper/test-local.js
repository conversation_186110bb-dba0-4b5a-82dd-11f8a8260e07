const EEVblogScraper = require('./src/scrapers/eevblogScraperLocal');
const S3Uploader = require('./src/utils/s3Uploader');
const fs = require('fs').promises;

// Test configuration
const TEST_CONFIG = {
  username: '0813',
  password: 'Qwalop125!',
  saveToFile: true, // Save to local file instead of S3 for testing
  limitForums: 1,   // Limit number of forums to scrape
  limitTopics: 2    // Limit topics per forum
};

async function testScraper() {
  const scraper = new EEVblogScraper({
    username: TEST_CONFIG.username,
    password: TEST_CONFIG.password
  });

  try {
    console.log('🚀 Starting local test...\n');
    
    // Initialize and login
    console.log('📱 Initializing browser...');
    await scraper.init();
    
    console.log('🔐 Logging in...');
    await scraper.login();
    console.log('✅ Login successful!\n');
    
    // Get forums
    console.log('📋 Fetching forum list...');
    const forums = await scraper.scrapeForumList();
    console.log(`✅ Found ${forums.length} forums\n`);
    
    // Test data collection
    const testData = {
      testDate: new Date().toISOString(),
      testType: 'local',
      forums: []
    };
    
    // Limit forums for testing
    const forumsToScrape = forums.slice(0, TEST_CONFIG.limitForums);
    
    for (const forum of forumsToScrape) {
      console.log(`\n📂 Scraping forum: ${forum.name}`);
      console.log(`   URL: ${forum.url}`);
      
      const forumData = {
        forumId: forum.id,
        forumName: forum.name,
        forumUrl: forum.url,
        topics: []
      };
      
      try {
        // Get first page of topics
        const { topics, hasNextPage } = await scraper.scrapeTopics(forum.url);
        console.log(`   ✅ Found ${topics.length} topics (hasNextPage: ${hasNextPage})`);
        
        // Limit topics for testing
        const topicsToScrape = topics.slice(0, TEST_CONFIG.limitTopics);
        
        for (const topic of topicsToScrape) {
          console.log(`\n   📄 Scraping topic: ${topic.title}`);
          console.log(`      Replies: ${topic.replies}, Views: ${topic.views}`);
          
          await scraper.delay(1000); // Rate limiting
          
          try {
            const topicData = await scraper.scrapeAllTopicPages(topic.url);
            console.log(`      ✅ Scraped ${topicData.posts.length} posts`);
            
            forumData.topics.push({
              topicId: topic.id,
              title: topicData.title,
              url: topic.url,
              metadata: {
                replies: topic.replies,
                views: topic.views,
                isPinned: topic.isPinned,
                isLocked: topic.isLocked
              },
              posts: topicData.posts.slice(0, 5) // Limit posts for test output
            });
          } catch (error) {
            console.error(`      ❌ Failed to scrape topic: ${error.message}`);
          }
        }
        
        testData.forums.push(forumData);
      } catch (error) {
        console.error(`   ❌ Failed to scrape forum: ${error.message}`);
      }
    }
    
    // Save test data
    if (TEST_CONFIG.saveToFile) {
      const filename = `test-output-${Date.now()}.json`;
      await fs.writeFile(filename, JSON.stringify(testData, null, 2));
      console.log(`\n💾 Test data saved to: ${filename}`);
    }
    
    // Display summary
    console.log('\n📊 Test Summary:');
    console.log(`   Forums scraped: ${testData.forums.length}`);
    console.log(`   Total topics: ${testData.forums.reduce((sum, f) => sum + f.topics.length, 0)}`);
    console.log(`   Total posts: ${testData.forums.reduce((sum, f) => 
      sum + f.topics.reduce((tSum, t) => tSum + t.posts.length, 0), 0)}`);
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    console.error(error.stack);
  } finally {
    console.log('\n🧹 Closing browser...');
    await scraper.close();
    console.log('✅ Test complete!');
  }
}

// Run test
testScraper().catch(console.error);