{"name": "eevblog-scraper", "version": "1.0.0", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.873.0", "@aws-sdk/client-sqs": "^3.873.0", "@sparticuz/chromium": "^138.0.2", "aws-sdk": "^2.1692.0", "puppeteer": "^24.17.0", "puppeteer-core": "^24.17.0"}, "devDependencies": {"serverless": "^4.18.1", "serverless-webpack": "^5.15.2", "webpack": "^5.101.3", "webpack-cli": "^6.0.1"}}