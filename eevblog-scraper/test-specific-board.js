const puppeteer = require('puppeteer');

async function testSpecificBoard() {
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
  
  try {
    // Test without login first to see structure
    console.log('Testing forum structure without login...');
    await page.goto('https://www.eevblog.com/forum/index.php?board=2.0', {
      waitUntil: 'networkidle2'
    });
    
    const pageInfo = await page.evaluate(() => {
      const info = {
        title: document.title,
        url: window.location.href,
        tables: document.querySelectorAll('table').length,
        messageIndex: document.querySelector('#messageindex') !== null,
        tableGrid: document.querySelector('table.table_grid') !== null,
      };
      
      // Look for different topic patterns
      const patterns = [
        { selector: '#messageindex .subject a', name: 'messageindex subject' },
        { selector: 'table tr td.subject a', name: 'table subject' },
        { selector: '.topic_table a[href*="topic="]', name: 'topic_table' },
        { selector: 'span[id^="msg_"] a', name: 'msg span' },
        { selector: '.windowbg a[href*="topic="]', name: 'windowbg' },
        { selector: '.windowbg2 a[href*="topic="]', name: 'windowbg2' }
      ];
      
      info.patterns = patterns.map(p => ({
        name: p.name,
        count: document.querySelectorAll(p.selector).length
      }));
      
      // Get first few links for debugging
      const allTopicLinks = Array.from(document.querySelectorAll('a[href*="topic="]'))
        .filter(a => !a.href.includes('action=') && a.textContent.trim())
        .slice(0, 3);
      
      info.sampleLinks = allTopicLinks.map(a => ({
        text: a.textContent.trim(),
        href: a.href,
        parent: a.parentElement.tagName,
        parentClass: a.parentElement.className
      }));
      
      return info;
    });
    
    console.log('Page info:', JSON.stringify(pageInfo, null, 2));
    
    // Now try with login
    console.log('\n\nTesting with login...');
    await page.goto('https://www.eevblog.com/forum/index.php?action=login', {
      waitUntil: 'networkidle2'
    });
    
    await page.type('input[name="user"]', '0813');
    await page.type('input[name="passwrd"]', 'Qwalop125!');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"][value="Login"]')
    ]);
    
    // Go back to board
    await page.goto('https://www.eevblog.com/forum/index.php?board=2.0', {
      waitUntil: 'networkidle2'
    });
    
    const loggedInInfo = await page.evaluate(() => {
      const info = {
        isLoggedIn: document.body.textContent.includes('Logout'),
        messageCount: document.querySelectorAll('#messageindex').length,
        topicRows: document.querySelectorAll('#messageindex tbody tr').length
      };
      
      // Get actual structure
      const messageIndex = document.querySelector('#messageindex');
      if (messageIndex) {
        const rows = messageIndex.querySelectorAll('tbody tr');
        info.rowData = Array.from(rows).slice(0, 3).map(row => {
          const subjectCell = row.querySelector('td.subject');
          const link = subjectCell ? subjectCell.querySelector('a') : null;
          
          return {
            hasSubjectCell: !!subjectCell,
            hasLink: !!link,
            linkText: link ? link.textContent.trim() : 'N/A',
            linkHref: link ? link.href : 'N/A',
            cellHTML: subjectCell ? subjectCell.innerHTML.substring(0, 200) : 'N/A'
          };
        });
      }
      
      return info;
    });
    
    console.log('\nLogged in info:', JSON.stringify(loggedInInfo, null, 2));
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

testSpecificBoard().catch(console.error);