const puppeteer = require('puppeteer');

async function quickTest() {
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
  
  try {
    // Test login first
    console.log('Testing login...');
    await page.goto('https://www.eevblog.com/forum/index.php?action=login', {
      waitUntil: 'networkidle2'
    });
    
    await page.type('input[name="user"]', '0813');
    await page.type('input[name="passwrd"]', 'Qwalop125!');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"][value="Login"]')
    ]);
    
    console.log('Login completed, checking cookies...');
    const cookies = await page.cookies();
    console.log('Cookies found:', cookies.map(c => c.name));
    
    // Navigate to a specific topic to test
    console.log('\nNavigating to a specific topic...');
    await page.goto('https://www.eevblog.com/forum/beginners/', {
      waitUntil: 'networkidle2'
    });
    
    // Check page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Look for topics with a more flexible approach
    const topicData = await page.evaluate(() => {
      // Find all links that look like topics
      const allLinks = Array.from(document.querySelectorAll('a[href*="topic="]'));
      
      // Filter to get actual topic links
      const topicLinks = allLinks.filter(link => {
        const href = link.href;
        return href.includes('topic=') && !href.includes('action=') && link.textContent.trim().length > 0;
      });
      
      return {
        totalLinks: allLinks.length,
        topicLinks: topicLinks.length,
        samples: topicLinks.slice(0, 5).map(link => ({
          text: link.textContent.trim(),
          href: link.href,
          parentClass: link.parentElement?.className
        }))
      };
    });
    
    console.log('\nTopic data:', JSON.stringify(topicData, null, 2));
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
    console.log('\nTest complete!');
  }
}

quickTest().catch(console.error);