const puppeteer = require('puppeteer');

async function debugScraper() {
  const browser = await puppeteer.launch({
    headless: false, // Show browser for debugging
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
  
  try {
    // Navigate to forum
    console.log('Navigating to forum...');
    await page.goto('https://www.eevblog.com/forum/beginners/', {
      waitUntil: 'networkidle2'
    });
    
    // Take screenshot
    await page.screenshot({ path: 'forum-page.png' });
    console.log('Screenshot saved as forum-page.png');
    
    // Debug selectors
    const selectors = {
      'table.table_grid': await page.$$eval('table.table_grid', els => els.length),
      'tbody.content tr': await page.$$eval('tbody.content tr', els => els.length),
      'td.subject': await page.$$eval('td.subject', els => els.length),
      'td.subject a': await page.$$eval('td.subject a', els => els.length),
      'span.new_posts a': await page.$$eval('span.new_posts a', els => els.length),
    };
    
    console.log('Selector counts:', selectors);
    
    // Try to get topic data with different selectors
    const topics = await page.evaluate(() => {
      // Try multiple selectors
      const selectors = [
        'td.subject span a',
        'td.subject a',
        'td.windowbg2 span a',
        'table.table_grid td.subject a'
      ];
      
      for (const selector of selectors) {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          console.log(`Found ${elements.length} topics with selector: ${selector}`);
          return {
            selector: selector,
            count: elements.length,
            sample: Array.from(elements).slice(0, 3).map(el => ({
              text: el.textContent,
              href: el.href
            }))
          };
        }
      }
      
      return { selector: 'none', count: 0, sample: [] };
    });
    
    console.log('Topic data:', topics);
    
    // Get page HTML structure
    const structure = await page.evaluate(() => {
      const mainTable = document.querySelector('table.table_grid');
      if (!mainTable) return 'No table.table_grid found';
      
      const tbody = mainTable.querySelector('tbody');
      const firstRow = tbody ? tbody.querySelector('tr') : null;
      
      return {
        hasTable: !!mainTable,
        hasTbody: !!tbody,
        rowCount: tbody ? tbody.querySelectorAll('tr').length : 0,
        firstRowHTML: firstRow ? firstRow.outerHTML.substring(0, 500) : 'No rows'
      };
    });
    
    console.log('Page structure:', structure);
    
  } catch (error) {
    console.error('Debug error:', error);
  }
  
  console.log('\nKeeping browser open for inspection. Press Ctrl+C to exit.');
  // Keep browser open for manual inspection
  await new Promise(() => {});
}

debugScraper().catch(console.error);