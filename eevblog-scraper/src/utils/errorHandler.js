class ErrorHandler {
  static async withRetry(fn, options = {}) {
    const {
      maxRetries = 3,
      initialDelay = 1000,
      maxDelay = 30000,
      backoffMultiplier = 2,
      retryableErrors = ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'TimeoutError']
    } = options;

    let lastError;
    let delay = initialDelay;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        // Check if error is retryable
        const isRetryable = retryableErrors.some(retryableError => 
          error.message.includes(retryableError) || 
          error.code === retryableError ||
          error.name === retryableError
        );

        if (!isRetryable || attempt === maxRetries) {
          throw error;
        }

        console.log(`Attempt ${attempt + 1} failed: ${error.message}. Retrying in ${delay}ms...`);
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Calculate next delay with exponential backoff
        delay = Math.min(delay * backoffMultiplier, maxDelay);
      }
    }

    throw lastError;
  }

  static wrapHandler(handler) {
    return async (event, context) => {
      try {
        return await handler(event, context);
      } catch (error) {
        console.error('Handler error:', error);
        
        // Log detailed error information
        console.error({
          errorName: error.name,
          errorMessage: error.message,
          errorStack: error.stack,
          event: JSON.stringify(event),
          context: {
            functionName: context.functionName,
            requestId: context.awsRequestId,
            remainingTime: context.getRemainingTimeInMillis()
          }
        });

        // Return appropriate error response
        return {
          statusCode: error.statusCode || 500,
          body: JSON.stringify({
            error: error.name || 'InternalServerError',
            message: error.message || 'An unexpected error occurred',
            requestId: context.awsRequestId
          })
        };
      }
    };
  }

  static createCustomError(message, statusCode = 500, code = 'CUSTOM_ERROR') {
    const error = new Error(message);
    error.statusCode = statusCode;
    error.code = code;
    return error;
  }
}

module.exports = ErrorHandler;