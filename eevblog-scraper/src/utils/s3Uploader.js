const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');

class S3Uploader {
  constructor(bucketName, region = 'us-east-1') {
    this.bucketName = bucketName;
    this.s3Client = new S3Client({ region });
  }

  async uploadData(data, keyPrefix = '') {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = now.toISOString().replace(/[:.]/g, '-');
    
    // Create S3 key with date prefix
    const key = `${year}/${month}/${day}/${keyPrefix}${keyPrefix ? '-' : ''}${timestamp}.json`;
    
    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      Body: JSON.stringify(data, null, 2),
      ContentType: 'application/json',
      Metadata: {
        'scraper-version': '1.0.0',
        'scrape-date': now.toISOString()
      }
    });

    try {
      const response = await this.s3Client.send(command);
      console.log(`Successfully uploaded to S3: ${key}`);
      return { key, etag: response.ETag };
    } catch (error) {
      console.error('S3 upload failed:', error);
      throw error;
    }
  }

  async uploadBatch(dataArray, keyPrefix = '') {
    const results = [];
    
    for (const data of dataArray) {
      try {
        const result = await this.uploadData(data, keyPrefix);
        results.push({ success: true, ...result });
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    
    return results;
  }
}

module.exports = S3Uploader;