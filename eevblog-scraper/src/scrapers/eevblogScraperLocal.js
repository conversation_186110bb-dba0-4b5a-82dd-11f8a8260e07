const puppeteer = require('puppeteer');
const ErrorHandler = require('../utils/errorHandler');

class EEVblogScraperLocal {
  constructor(credentials) {
    this.baseUrl = 'https://www.eevblog.com/forum';
    this.username = credentials.username;
    this.password = credentials.password;
    this.browser = null;
    this.page = null;
  }

  async init() {
    // Use regular puppeteer for local testing
    this.browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    // Set timeout for navigation
    await this.page.setDefaultNavigationTimeout(30000);
  }

  async login() {
    return await ErrorHandler.withRetry(async () => {
      await this.page.goto(`${this.baseUrl}/index.php?action=login`, {
        waitUntil: 'networkidle2'
      });

      // Fill login form
      await this.page.type('input[name="user"]', this.username);
      await this.page.type('input[name="passwrd"]', this.password);
      
      // Submit login
      await Promise.all([
        this.page.waitForNavigation({ waitUntil: 'networkidle2' }),
        this.page.click('input[type="submit"][value="Login"]')
      ]);

      // Check if login was successful
      const cookies = await this.page.cookies();
      const hasSessionCookie = cookies.some(cookie => 
        cookie.name.includes('PHPSESSID') || cookie.name.includes('SMFCookie')
      );

      if (!hasSessionCookie) {
        throw ErrorHandler.createCustomError('Login failed - no session cookie found', 401, 'LOGIN_FAILED');
      }

      return true;
    }, {
      maxRetries: 3,
      retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'TimeoutError', 'LOGIN_FAILED']
    });
  }

  async scrapeForumList() {
    await this.page.goto(this.baseUrl, { waitUntil: 'networkidle2' });
    
    const forums = await this.page.evaluate(() => {
      const forumElements = document.querySelectorAll('table.table_list tbody.content td.info a.subject');
      
      return Array.from(forumElements).map(elem => ({
        name: elem.textContent.trim(),
        url: elem.href,
        id: elem.href.match(/board=(\d+)/)?.[1] || null
      }));
    });

    return forums;
  }

  async scrapeTopics(forumUrl, pageNum = 0) {
    const url = pageNum > 0 ? `${forumUrl}.${pageNum * 30}` : forumUrl;
    await this.page.goto(url, { waitUntil: 'networkidle2' });
    
    const topics = await this.page.evaluate(() => {
      // Get all topic rows from the message index
      const topicRows = document.querySelectorAll('#messageindex tbody tr');
      
      return Array.from(topicRows).map(row => {
        // Skip header rows
        if (row.querySelector('th')) return null;
        
        const linkElem = row.querySelector('td.subject a');
        const statsElem = row.querySelector('td.stats');
        const lastPostElem = row.querySelector('td.lastpost');
        
        if (!linkElem) return null;
        
        // Extract stats
        const statsText = statsElem?.textContent || '';
        const replies = statsText.match(/(\d+)\s+Replies/)?.[1] || '0';
        const views = statsText.match(/(\d+)\s+Views/)?.[1] || '0';
        
        // Extract topic ID from URL
        const topicMatch = linkElem.href.match(/topic=(\d+)/);
        const topicId = topicMatch ? topicMatch[1] : null;
        
        return {
          title: linkElem.textContent.trim(),
          url: linkElem.href,
          id: topicId,
          replies: parseInt(replies),
          views: parseInt(views),
          lastPost: lastPostElem?.textContent.trim() || '',
          isPinned: row.querySelector('img[src*="sticky"]') !== null,
          isLocked: row.querySelector('img[src*="lock"]') !== null
        };
      }).filter(Boolean);
    });

    // Check if there are more pages
    const hasNextPage = await this.page.evaluate(() => {
      const pageLinks = document.querySelector('.pagelinks');
      if (!pageLinks) return false;
      
      // Look for the next page link
      const links = Array.from(pageLinks.querySelectorAll('a'));
      return links.some(link => link.textContent === '»');
    });

    return { topics, hasNextPage };
  }

  async scrapeTopic(topicUrl) {
    await this.page.goto(topicUrl, { waitUntil: 'networkidle2' });
    
    // Get topic metadata
    const metadata = await this.page.evaluate(() => {
      const titleElem = document.querySelector('div.navigate_section li.last a');
      const pageLinksElem = document.querySelector('div.pagelinks');
      
      return {
        title: titleElem?.textContent.trim() || '',
        totalPages: pageLinksElem ? 
          Math.max(...Array.from(pageLinksElem.querySelectorAll('a.navPages'))
            .map(a => parseInt(a.textContent) || 0)) || 1 : 1
      };
    });

    // Scrape all posts on current page
    const posts = await this.page.evaluate(() => {
      const postElements = document.querySelectorAll('div.post_wrapper');
      
      return Array.from(postElements).map(post => {
        const authorElem = post.querySelector('div.poster h4 a');
        const timeElem = post.querySelector('div.postarea div.smalltext');
        const contentElem = post.querySelector('div.post div.inner');
        const postIdElem = post.querySelector('div.postarea div.smalltext a[href*="#msg"]');
        
        // Extract post number from "Reply #X on:"
        const postNumberMatch = timeElem?.textContent.match(/Reply #(\d+)/);
        const postNumber = postNumberMatch ? parseInt(postNumberMatch[1]) : 0;
        
        // Extract timestamp
        const timeMatch = timeElem?.textContent.match(/on:\s*(.+?)(?:\s*»|$)/);
        const timestamp = timeMatch ? timeMatch[1].trim() : '';
        
        return {
          id: postIdElem?.href.match(/msg(\d+)/)?.[1] || null,
          author: authorElem?.textContent.trim() || 'Guest',
          authorUrl: authorElem?.href || null,
          timestamp: timestamp,
          postNumber: postNumber,
          content: contentElem?.textContent.trim() || '',
          quotedPosts: Array.from(post.querySelectorAll('div.quoteheader'))
            .map(quote => quote.textContent.replace('Quote from:', '').trim())
        };
      });
    });

    return { ...metadata, posts };
  }

  async scrapeAllTopicPages(topicUrl) {
    const firstPage = await this.scrapeTopic(topicUrl);
    const allPosts = [...firstPage.posts];
    
    // Scrape remaining pages if any
    for (let page = 1; page < firstPage.totalPages; page++) {
      await this.delay(1000); // Rate limiting
      const pageUrl = `${topicUrl}.${page * 20}`;
      const pageData = await this.scrapeTopic(pageUrl);
      allPosts.push(...pageData.posts);
    }

    return {
      title: firstPage.title,
      totalPages: firstPage.totalPages,
      posts: allPosts
    };
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

module.exports = EEVblogScraperLocal;