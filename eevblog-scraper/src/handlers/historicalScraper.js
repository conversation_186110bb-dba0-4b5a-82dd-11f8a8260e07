const EEVblogScraper = require('../scrapers/eevblogScraper');
const S3Uploader = require('../utils/s3Uploader');

exports.handler = async (event, context) => {
  const scraper = new EEVblogScraper({
    username: process.env.FORUM_USERNAME,
    password: process.env.FORUM_PASSWORD
  });
  
  const s3Uploader = new S3Uploader(process.env.S3_BUCKET);
  
  try {
    // Process SQS messages
    for (const record of event.Records) {
      const message = JSON.parse(record.body);
      console.log('Processing historical scrape job:', message);
      
      const { forumId, forumName, forumUrl, startPage, endPage } = message;
      
      // Initialize browser and login
      await scraper.init();
      await scraper.login();
      
      const historicalData = {
        scrapeDate: new Date().toISOString(),
        scrapeType: 'historical',
        forumId,
        forumName,
        forumUrl,
        pageRange: { start: startPage, end: endPage },
        topics: []
      };
      
      // Scrape specified page range
      for (let page = startPage; page <= endPage; page++) {
        console.log(`Scraping page ${page} of forum ${forumName}`);
        
        try {
          const { topics, hasNextPage } = await scraper.scrapeTopics(forumUrl, page);
          
          // Get full topic data
          for (const topic of topics) {
            console.log(`  Scraping historical topic: ${topic.title}`);
            await scraper.delay(1000); // Rate limiting
            
            try {
              const topicData = await scraper.scrapeAllTopicPages(topic.url);
              historicalData.topics.push({
                topicId: topic.id,
                title: topicData.title,
                url: topic.url,
                metadata: {
                  replies: topic.replies,
                  views: topic.views,
                  isPinned: topic.isPinned,
                  isLocked: topic.isLocked,
                  lastPost: topic.lastPost
                },
                posts: topicData.posts
              });
            } catch (error) {
              console.error(`  Failed to scrape topic ${topic.id}:`, error.message);
            }
          }
          
          // Stop if no more pages
          if (!hasNextPage && page === endPage) {
            console.log('Reached last page of forum');
            break;
          }
          
          await scraper.delay(1000); // Rate limiting between pages
        } catch (error) {
          console.error(`Failed to scrape page ${page}:`, error.message);
        }
      }
      
      // Upload to S3
      const uploadResult = await s3Uploader.uploadData(
        historicalData, 
        `historical/${forumId}/pages-${startPage}-${endPage}`
      );
      console.log('Historical data uploaded:', uploadResult);
      
      await scraper.close();
    }
    
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Historical scrape completed successfully',
        recordsProcessed: event.Records.length
      })
    };
    
  } catch (error) {
    console.error('Historical scraper error:', error);
    await scraper.close();
    
    // Re-throw to trigger SQS retry
    throw error;
  }
};