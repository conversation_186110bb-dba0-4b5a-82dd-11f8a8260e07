const { SQSClient, SendMessageBatchCommand } = require('@aws-sdk/client-sqs');
const EEVblogScraper = require('../scrapers/eevblogScraper');

exports.handler = async (event, context) => {
  const sqsClient = new SQSClient({ region: process.env.AWS_REGION });
  const queueUrl = process.env.QUEUE_URL;
  
  const scraper = new EEVblogScraper({
    username: process.env.FORUM_USERNAME,
    password: process.env.FORUM_PASSWORD
  });
  
  try {
    console.log('Coordinator starting historical scrape job distribution');
    
    // Initialize and get forum list
    await scraper.init();
    await scraper.login();
    const forums = await scraper.scrapeForumList();
    
    const messages = [];
    const batchSize = 5; // Pages per job
    
    // Create jobs for each forum
    for (const forum of forums) {
      console.log(`Creating jobs for forum: ${forum.name}`);
      
      // Determine total pages (estimate based on typical forum size)
      // In production, you'd want to check actual page count
      const estimatedPages = 50; // Adjust based on forum size
      
      // Split into batches
      for (let startPage = 0; startPage < estimatedPages; startPage += batchSize) {
        const endPage = Math.min(startPage + batchSize - 1, estimatedPages - 1);
        
        messages.push({
          Id: `${forum.id}-${startPage}`,
          MessageBody: JSON.stringify({
            forumId: forum.id,
            forumName: forum.name,
            forumUrl: forum.url,
            startPage,
            endPage
          })
        });
      }
    }
    
    // Send messages to SQS in batches of 10 (SQS limit)
    const messageChunks = [];
    for (let i = 0; i < messages.length; i += 10) {
      messageChunks.push(messages.slice(i, i + 10));
    }
    
    console.log(`Sending ${messages.length} jobs to SQS in ${messageChunks.length} batches`);
    
    for (const chunk of messageChunks) {
      const command = new SendMessageBatchCommand({
        QueueUrl: queueUrl,
        Entries: chunk
      });
      
      try {
        await sqsClient.send(command);
        console.log(`Sent batch of ${chunk.length} messages`);
      } catch (error) {
        console.error('Failed to send message batch:', error);
      }
    }
    
    await scraper.close();
    
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Historical scrape jobs distributed',
        forumsCount: forums.length,
        jobsCreated: messages.length
      })
    };
    
  } catch (error) {
    console.error('Coordinator error:', error);
    await scraper.close();
    
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Coordinator failed',
        message: error.message
      })
    };
  }
};