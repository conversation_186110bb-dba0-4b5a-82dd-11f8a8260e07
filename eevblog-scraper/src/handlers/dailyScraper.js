const EEVblogScraper = require('../scrapers/eevblogScraper');
const S3Uploader = require('../utils/s3Uploader');

exports.handler = async (event, context) => {
  const scraper = new EEVblogScraper({
    username: process.env.FORUM_USERNAME,
    password: process.env.FORUM_PASSWORD
  });
  
  const s3Uploader = new S3Uploader(process.env.S3_BUCKET);
  
  try {
    console.log('Starting daily scrape...');
    
    // Initialize browser and login
    await scraper.init();
    await scraper.login();
    console.log('Successfully logged in');
    
    // Get list of forums
    const forums = await scraper.scrapeForumList();
    console.log(`Found ${forums.length} forums`);
    
    const dailyData = {
      scrapeDate: new Date().toISOString(),
      scrapeType: 'daily',
      forums: []
    };
    
    // Scrape recent topics from each forum
    for (const forum of forums) {
      console.log(`Scraping forum: ${forum.name}`);
      
      try {
        // Get first page of topics (most recent)
        const { topics } = await scraper.scrapeTopics(forum.url);
        
        const forumData = {
          forumId: forum.id,
          forumName: forum.name,
          forumUrl: forum.url,
          topics: []
        };
        
        // Get details for recent topics (limit to avoid timeout)
        const recentTopics = topics.slice(0, 5); // Top 5 most recent
        
        for (const topic of recentTopics) {
          console.log(`  Scraping topic: ${topic.title}`);
          await scraper.delay(1000); // Rate limiting
          
          try {
            const topicData = await scraper.scrapeAllTopicPages(topic.url);
            forumData.topics.push({
              topicId: topic.id,
              title: topicData.title,
              url: topic.url,
              metadata: {
                replies: topic.replies,
                views: topic.views,
                isPinned: topic.isPinned,
                isLocked: topic.isLocked,
                lastPost: topic.lastPost
              },
              posts: topicData.posts
            });
          } catch (error) {
            console.error(`  Failed to scrape topic ${topic.id}:`, error.message);
          }
        }
        
        dailyData.forums.push(forumData);
        await scraper.delay(1000); // Rate limiting between forums
      } catch (error) {
        console.error(`Failed to scrape forum ${forum.id}:`, error.message);
      }
    }
    
    // Upload to S3
    const uploadResult = await s3Uploader.uploadData(dailyData, 'daily');
    console.log('Upload complete:', uploadResult);
    
    await scraper.close();
    
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Daily scrape completed successfully',
        forumsScraped: dailyData.forums.length,
        s3Key: uploadResult.key
      })
    };
    
  } catch (error) {
    console.error('Daily scraper error:', error);
    await scraper.close();
    
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Daily scrape failed',
        message: error.message
      })
    };
  }
};