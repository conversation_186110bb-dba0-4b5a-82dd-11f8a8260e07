# EEVblog Forum Scraper

A serverless AWS Lambda-based solution for scraping EEVblog forums and storing data in S3.

## Architecture

- **Puppeteer-based scraper** for handling authentication and navigation
- **AWS Lambda functions** for scheduled and batch processing
- **S3 storage** with date-prefixed JSON files
- **SQS queue** for managing historical data workloads

## Setup

### Prerequisites

- Node.js 18.x
- AWS CLI configured with appropriate credentials
- Serverless Framework installed globally

### Installation

```bash
npm install
```

### Configuration

1. Set environment variables:
```bash
export FORUM_USERNAME="0813"
export FORUM_PASSWORD="Qwalop125!"
export S3_BUCKET="your-s3-bucket-name"
```

2. Deploy to AWS:
```bash
serverless deploy --stage prod
```

## Local Testing

Run the local test script to verify the scraper works:

```bash
node test-local.js
```

This will:
- Test login functionality
- Scrape a limited set of forums/topics
- Save output to a local JSON file

## Lambda Functions

### Daily Scraper
- Runs at 2 AM UTC daily
- Scrapes recent topics from all forums
- Uploads to S3 with path: `YYYY/MM/DD/daily-timestamp.json`

### Historical Scraper
- Processes batches from SQS queue
- Handles page ranges for historical data
- Rate-limited to 1 request/second

### Coordinator
- Splits large historical scraping jobs
- Distributes work via SQS
- Prevents Lambda timeout issues

## Data Format

```json
{
  "scrapeDate": "2025-08-23T10:00:00Z",
  "scrapeType": "daily|historical",
  "forums": [{
    "forumId": "123",
    "forumName": "General Chat",
    "topics": [{
      "topicId": "456",
      "title": "Topic Title",
      "metadata": {
        "replies": 10,
        "views": 100,
        "isPinned": false,
        "isLocked": false
      },
      "posts": [{
        "id": "789",
        "author": "username",
        "timestamp": "August 23, 2025, 10:00:00 am",
        "content": "Post content..."
      }]
    }]
  }]
}
```

## Error Handling

- Automatic retry with exponential backoff
- SQS Dead Letter Queue for persistent failures
- CloudWatch logging for all executions
- Authentication validation and retry logic

## Monitoring

View logs in CloudWatch:
```bash
serverless logs -f dailyScraper -t
serverless logs -f historicalScraper -t
```

## Manual Invocation

Test functions locally:
```bash
serverless invoke local -f dailyScraper
```

Invoke deployed functions:
```bash
serverless invoke -f coordinator
```